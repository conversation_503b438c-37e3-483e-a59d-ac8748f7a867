/* Fade animations */
.animate-fade-in {
  opacity: 0;
  animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Slide animations */
.animate-slide-up {
  opacity: 0;
  transform: translateY(20px);
  animation: slideUp 0.8s ease-out forwards;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Feature animations */
.animate-feature {
  animation: slideUp 0.8s ease-out forwards;
}

/* Transition animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Hover animations */
.hover-effect:hover .hover-block {
  transform: rotate(5deg) scale(1.02);
}

/* CTA section animations */
.cta-section {
  opacity: 0;
  transform: translateY(20px);
}

.cta-section.animate-feature {
  animation: slideUp 0.8s ease-out forwards;
} 