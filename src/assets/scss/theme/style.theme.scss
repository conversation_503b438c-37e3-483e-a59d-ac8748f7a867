// ===== BLOG THEME STYLES =====

// ===== LAYOUT COMPONENTS =====
.blog-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.blog-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  
  .container {
    @include container;
    @include flex-between;
    padding-top: var(--space-4);
    padding-bottom: var(--space-4);
  }
  
  .logo {
    @include text-style(var(--text-xl), var(--font-bold));
    color: var(--color-primary);
  }
  
  .nav {
    display: flex;
    gap: var(--space-6);
    
    a {
      @include text-style(var(--text-base), var(--font-medium));
      color: var(--text-secondary);
      transition: color var(--transition-fast);
      
      &:hover {
        color: var(--color-primary);
        text-decoration: none;
      }
      
      &.active {
        color: var(--color-primary);
      }
    }
  }
}

.blog-main {
  flex: 1;
  padding: var(--space-8) 0;
}

.blog-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  padding: var(--space-8) 0;
  margin-top: auto;
  
  .container {
    @include container;
    text-align: center;
    color: var(--text-tertiary);
  }
}

// ===== BLOG COMPONENTS =====
.blog-hero {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  color: var(--text-inverse);
  padding: var(--space-16) 0;
  text-align: center;
  
  .container {
    @include container;
  }
  
  h1 {
    @include heading-1;
    color: var(--text-inverse);
    margin-bottom: var(--space-4);
  }
  
  p {
    @include body-large;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
  }
}

.blog-tags {
  margin-bottom: var(--space-8);
  
  .container {
    @include container;
  }
  
  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-3);
    justify-content: center;
    
    @include respond-to('md') {
      justify-content: flex-start;
    }
  }
  
  .tag {
    @include button-secondary;
    @include text-style(var(--text-sm), var(--font-medium));
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
    
    &:hover {
      background: var(--color-primary);
      color: var(--text-inverse);
      border-color: var(--color-primary);
      text-decoration: none;
    }
    
    &.active {
      background: var(--color-primary);
      color: var(--text-inverse);
      border-color: var(--color-primary);
    }
  }
}

.blog-list {
  .container {
    @include container;
  }
  
  .posts-grid {
    display: grid;
    gap: var(--space-8);
    
    @include respond-to('md') {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include respond-to('lg') {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

.blog-post-card {
  @include card;
  overflow: hidden;
  transition: all var(--transition-normal);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }
  
  .post-image {
    @include aspect-ratio(16, 9);
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform var(--transition-slow);
    }
  }
  
  &:hover .post-image img {
    transform: scale(1.05);
  }
  
  .post-content {
    padding: var(--space-6);
  }
  
  .post-title {
    @include heading-4;
    margin-bottom: var(--space-3);
    
    a {
      color: var(--text-primary);
      text-decoration: none;
      
      &:hover {
        color: var(--color-primary);
      }
    }
  }
  
  .post-excerpt {
    @include body-normal;
    @include line-clamp(3);
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
  }
  
  .post-meta {
    @include flex-between;
    @include text-style(var(--text-sm), var(--font-medium));
    color: var(--text-tertiary);
  }
  
  .post-date {
    display: flex;
    align-items: center;
    gap: var(--space-2);
  }
  
  .post-tags {
    display: flex;
    gap: var(--space-2);
    
    .tag {
      @include text-style(var(--text-xs), var(--font-medium));
      background: var(--bg-secondary);
      color: var(--color-primary);
      padding: var(--space-1) var(--space-2);
      border-radius: var(--radius-sm);
    }
  }
}

.blog-post {
  .container {
    @include container;
    max-width: 800px;
  }
  
  .post-header {
    text-align: center;
    margin-bottom: var(--space-12);
    
    .post-title {
      @include heading-1;
      margin-bottom: var(--space-6);
    }
    
    .post-meta {
      @include flex-center;
      gap: var(--space-4);
      @include text-style(var(--text-base), var(--font-medium));
      color: var(--text-secondary);
      margin-bottom: var(--space-8);
    }
    
    .post-image {
      @include aspect-ratio(16, 9);
      border-radius: var(--radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  
  .post-content {
    @include body-large;
    line-height: var(--leading-relaxed);
    
    // Enhanced typography for blog content
    h2, h3, h4, h5, h6 {
      margin-top: var(--space-12);
      margin-bottom: var(--space-6);
    }
    
    p {
      margin-bottom: var(--space-6);
    }
    
    ul, ol {
      margin-bottom: var(--space-6);
      
      li {
        margin-bottom: var(--space-3);
      }
    }
    
    blockquote {
      margin: var(--space-8) 0;
      padding: var(--space-6);
      background: var(--bg-secondary);
      border-radius: var(--radius-md);
    }
    
    pre {
      margin: var(--space-8) 0;
    }
    
    img {
      margin: var(--space-8) auto;
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-md);
    }
  }
  
  .post-tags {
    margin-top: var(--space-12);
    padding-top: var(--space-8);
    border-top: 1px solid var(--border-primary);
    
    .tags-title {
      @include text-style(var(--text-base), var(--font-semibold));
      margin-bottom: var(--space-4);
    }
    
    .tags-list {
      display: flex;
      flex-wrap: wrap;
      gap: var(--space-3);
      
      .tag {
        @include button-secondary;
        @include text-style(var(--text-sm), var(--font-medium));
        padding: var(--space-2) var(--space-4);
        border-radius: var(--radius-full);
      }
    }
  }
}

.post-navigation {
  margin-top: var(--space-16);
  padding-top: var(--space-8);
  border-top: 1px solid var(--border-primary);
  
  .nav-links {
    display: grid;
    gap: var(--space-6);
    
    @include respond-to('md') {
      grid-template-columns: 1fr 1fr;
    }
  }
  
  .nav-link {
    @include card;
    padding: var(--space-6);
    text-decoration: none;
    transition: all var(--transition-normal);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
    
    .nav-label {
      @include text-style(var(--text-sm), var(--font-medium));
      color: var(--text-tertiary);
      margin-bottom: var(--space-2);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    .nav-title {
      @include text-style(var(--text-lg), var(--font-semibold));
      color: var(--text-primary);
      @include line-clamp(2);
    }
    
    &.next {
      text-align: right;
    }
  }
}

// ===== RESPONSIVE ADJUSTMENTS =====
@include respond-below('md') {
  .blog-header {
    .nav {
      display: none; // Hide navigation on mobile, implement mobile menu if needed
    }
  }
  
  .blog-hero {
    padding: var(--space-12) 0;
    
    h1 {
      @include text-style(var(--text-3xl), var(--font-bold), var(--leading-tight));
    }
  }
  
  .blog-post {
    .post-header {
      .post-meta {
        flex-direction: column;
        gap: var(--space-2);
      }
    }
  }
}

// ===== LOADING STATES =====
.loading {
  @include flex-center;
  padding: var(--space-8);
  
  .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-primary);
    border-top-color: var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// ===== EMPTY STATES =====
.empty-state {
  @include flex-center;
  flex-direction: column;
  padding: var(--space-16) var(--space-4);
  text-align: center;
  
  .empty-icon {
    width: 64px;
    height: 64px;
    margin-bottom: var(--space-6);
    opacity: 0.5;
  }
  
  .empty-title {
    @include heading-3;
    margin-bottom: var(--space-4);
  }
  
  .empty-description {
    @include body-normal;
    color: var(--text-secondary);
    max-width: 400px;
  }
}
