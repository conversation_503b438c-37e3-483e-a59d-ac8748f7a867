// ===== COLORS =====
:root {
  // Primary Colors
  --color-primary: #3b82f6;
  --color-primary-dark: #2563eb;
  --color-primary-light: #60a5fa;
  
  // Secondary Colors
  --color-secondary: #6366f1;
  --color-secondary-dark: #4f46e5;
  --color-secondary-light: #818cf8;
  
  // Neutral Colors
  --color-white: #ffffff;
  --color-black: #000000;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  // Status Colors
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  // Background Colors
  --bg-primary: var(--color-white);
  --bg-secondary: var(--color-gray-50);
  --bg-tertiary: var(--color-gray-100);
  
  // Text Colors
  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-600);
  --text-tertiary: var(--color-gray-400);
  --text-inverse: var(--color-white);
  
  // Border Colors
  --border-primary: var(--color-gray-200);
  --border-secondary: var(--color-gray-300);
  --border-focus: var(--color-primary);
  
  // Shadow Colors
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

// Dark Theme
[data-theme="dark"] {
  // Background Colors
  --bg-primary: var(--color-gray-900);
  --bg-secondary: var(--color-gray-800);
  --bg-tertiary: var(--color-gray-700);
  
  // Text Colors
  --text-primary: var(--color-gray-100);
  --text-secondary: var(--color-gray-300);
  --text-tertiary: var(--color-gray-500);
  
  // Border Colors
  --border-primary: var(--color-gray-700);
  --border-secondary: var(--color-gray-600);
}

// ===== TYPOGRAPHY =====
:root {
  // Font Families
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-serif: 'Merriweather', Georgia, serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  // Font Sizes
  --text-xs: 0.75rem;     // 12px
  --text-sm: 0.875rem;    // 14px
  --text-base: 1rem;      // 16px
  --text-lg: 1.125rem;    // 18px
  --text-xl: 1.25rem;     // 20px
  --text-2xl: 1.5rem;     // 24px
  --text-3xl: 1.875rem;   // 30px
  --text-4xl: 2.25rem;    // 36px
  --text-5xl: 3rem;       // 48px
  --text-6xl: 3.75rem;    // 60px
  
  // Line Heights
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  // Font Weights
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
}

// ===== SPACING =====
:root {
  --space-0: 0;
  --space-1: 0.25rem;     // 4px
  --space-2: 0.5rem;      // 8px
  --space-3: 0.75rem;     // 12px
  --space-4: 1rem;        // 16px
  --space-5: 1.25rem;     // 20px
  --space-6: 1.5rem;      // 24px
  --space-8: 2rem;        // 32px
  --space-10: 2.5rem;     // 40px
  --space-12: 3rem;       // 48px
  --space-16: 4rem;       // 64px
  --space-20: 5rem;       // 80px
  --space-24: 6rem;       // 96px
  --space-32: 8rem;       // 128px
  --space-40: 10rem;      // 160px
  --space-48: 12rem;      // 192px
  --space-56: 14rem;      // 224px
  --space-64: 16rem;      // 256px
}

// ===== BREAKPOINTS =====
$breakpoints: (
  'xs': 480px,
  'sm': 640px,
  'md': 768px,
  'lg': 1024px,
  'xl': 1280px,
  '2xl': 1536px
);

// ===== GRID =====
:root {
  --container-max-width: 1200px;
  --container-padding: var(--space-4);
  --grid-gap: var(--space-6);
  --grid-columns: 12;
}

// ===== BORDER RADIUS =====
:root {
  --radius-none: 0;
  --radius-sm: 0.125rem;   // 2px
  --radius-md: 0.375rem;   // 6px
  --radius-lg: 0.5rem;     // 8px
  --radius-xl: 0.75rem;    // 12px
  --radius-2xl: 1rem;      // 16px
  --radius-3xl: 1.5rem;    // 24px
  --radius-full: 9999px;
}

// ===== TRANSITIONS =====
:root {
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

// ===== Z-INDEX =====
:root {
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}
