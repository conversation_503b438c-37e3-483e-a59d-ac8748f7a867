@use 'sass:map';

// ===== RESPONSIVE MIXINS =====
@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (min-width: map.get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

@mixin respond-below($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (max-width: map.get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

@mixin respond-between($min, $max) {
  @if map.has-key($breakpoints, $min) and map.has-key($breakpoints, $max) {
    @media (min-width: map.get($breakpoints, $min)) and (max-width: map.get($breakpoints, $max) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoints: #{$min} or #{$max}.";
  }
}

// ===== LAYOUT MIXINS =====
@mixin container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin grid-center {
  display: grid;
  place-items: center;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin absolute-fill {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

// ===== TYPOGRAPHY MIXINS =====
@mixin text-style($size, $weight: var(--font-normal), $line-height: var(--leading-normal)) {
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
}

@mixin heading-1 {
  @include text-style(var(--text-4xl), var(--font-bold), var(--leading-tight));
  
  @include respond-to('md') {
    @include text-style(var(--text-5xl), var(--font-bold), var(--leading-tight));
  }
  
  @include respond-to('lg') {
    @include text-style(var(--text-6xl), var(--font-bold), var(--leading-tight));
  }
}

@mixin heading-2 {
  @include text-style(var(--text-3xl), var(--font-bold), var(--leading-tight));
  
  @include respond-to('md') {
    @include text-style(var(--text-4xl), var(--font-bold), var(--leading-tight));
  }
}

@mixin heading-3 {
  @include text-style(var(--text-2xl), var(--font-semibold), var(--leading-snug));
  
  @include respond-to('md') {
    @include text-style(var(--text-3xl), var(--font-semibold), var(--leading-snug));
  }
}

@mixin heading-4 {
  @include text-style(var(--text-xl), var(--font-semibold), var(--leading-snug));
  
  @include respond-to('md') {
    @include text-style(var(--text-2xl), var(--font-semibold), var(--leading-snug));
  }
}

@mixin body-large {
  @include text-style(var(--text-lg), var(--font-normal), var(--leading-relaxed));
}

@mixin body-normal {
  @include text-style(var(--text-base), var(--font-normal), var(--leading-normal));
}

@mixin body-small {
  @include text-style(var(--text-sm), var(--font-normal), var(--leading-normal));
}

@mixin caption {
  @include text-style(var(--text-xs), var(--font-medium), var(--leading-normal));
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

// ===== VISUAL MIXINS =====
@mixin card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-normal);
  
  &:hover {
    box-shadow: var(--shadow-md);
  }
}

@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background: var(--color-primary);
  color: var(--text-inverse);
  
  &:hover:not(:disabled) {
    background: var(--color-primary-dark);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

@mixin button-secondary {
  @include button-base;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  
  &:hover:not(:disabled) {
    background: var(--bg-tertiary);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

@mixin input-base {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: border-color var(--transition-fast);
  
  &:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
  }
  
  &::placeholder {
    color: var(--text-tertiary);
  }
}

// ===== ANIMATION MIXINS =====
@mixin fade-in($duration: var(--transition-normal)) {
  animation: fadeIn $duration ease-out;
}

@mixin slide-up($duration: var(--transition-normal)) {
  animation: slideUp $duration ease-out;
}

@mixin scale-in($duration: var(--transition-fast)) {
  animation: scaleIn $duration ease-out;
}

// ===== UTILITY MIXINS =====
@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin line-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@mixin aspect-ratio($width, $height) {
  aspect-ratio: $width / $height;
  
  @supports not (aspect-ratio: 1) {
    &::before {
      content: '';
      display: block;
      padding-top: percentage($height / $width);
    }
  }
}
