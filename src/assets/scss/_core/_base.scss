// ===== RESET & NORMALIZE =====
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-sans);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

// ===== TYPOGRAPHY =====
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

h1 {
  @include heading-1;
}

h2 {
  @include heading-2;
}

h3 {
  @include heading-3;
}

h4 {
  @include heading-4;
}

h5 {
  @include text-style(var(--text-lg), var(--font-semibold), var(--leading-snug));
}

h6 {
  @include text-style(var(--text-base), var(--font-semibold), var(--leading-snug));
}

p {
  margin-bottom: var(--space-4);
  color: var(--text-secondary);
  
  &:last-child {
    margin-bottom: 0;
  }
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-dark);
    text-decoration: underline;
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
}

strong, b {
  font-weight: var(--font-semibold);
}

em, i {
  font-style: italic;
}

small {
  font-size: var(--text-sm);
}

code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background: var(--bg-secondary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  color: var(--color-secondary);
}

pre {
  font-family: var(--font-mono);
  background: var(--bg-secondary);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  overflow-x: auto;
  margin-bottom: var(--space-4);
  
  code {
    background: none;
    padding: 0;
    color: inherit;
  }
}

blockquote {
  border-left: 4px solid var(--color-primary);
  padding-left: var(--space-4);
  margin: var(--space-6) 0;
  font-style: italic;
  color: var(--text-secondary);
}

// ===== LISTS =====
ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
  
  &:last-child {
    margin-bottom: 0;
  }
}

li {
  margin-bottom: var(--space-2);
  color: var(--text-secondary);
  
  &:last-child {
    margin-bottom: 0;
  }
}

// ===== MEDIA =====
img, video {
  max-width: 100%;
  height: auto;
  display: block;
}

figure {
  margin-bottom: var(--space-6);
  
  img {
    border-radius: var(--radius-md);
  }
  
  figcaption {
    @include caption;
    color: var(--text-tertiary);
    text-align: center;
    margin-top: var(--space-2);
  }
}

// ===== FORMS =====
input, textarea, select {
  @include input-base;
}

textarea {
  resize: vertical;
  min-height: 120px;
}

select {
  cursor: pointer;
}

button {
  @include button-primary;
}

label {
  display: block;
  font-weight: var(--font-medium);
  margin-bottom: var(--space-2);
  color: var(--text-primary);
}

fieldset {
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

legend {
  font-weight: var(--font-semibold);
  padding: 0 var(--space-2);
  color: var(--text-primary);
}

// ===== TABLES =====
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--space-6);
}

th, td {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

th {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  background: var(--bg-secondary);
}

td {
  color: var(--text-secondary);
}

// ===== UTILITY CLASSES =====
.container {
  @include container;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.visually-hidden {
  @include visually-hidden;
}

.truncate {
  @include truncate;
}

.line-clamp-2 {
  @include line-clamp(2);
}

.line-clamp-3 {
  @include line-clamp(3);
}

// ===== ANIMATIONS =====
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// ===== FOCUS MANAGEMENT =====
.focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

// ===== PRINT STYLES =====
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  
  pre, blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  
  thead {
    display: table-header-group;
  }
  
  tr, img {
    page-break-inside: avoid;
  }
  
  img {
    max-width: 100% !important;
  }
  
  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }
  
  h2, h3 {
    page-break-after: avoid;
  }
}
