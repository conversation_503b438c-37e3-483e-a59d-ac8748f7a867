<template>
  <div class="h-card vcard" itemscope itemtype="https://schema.org/Organization">
    <div class="hidden">
      <!-- Microformats h-card -->
      <p class="p-name" itemprop="name">Fillify</p>
      <p class="p-org" itemprop="organization">Fillify Team</p>
      <p class="u-url" itemprop="url">https://fillify.tech</p>
      <img class="u-photo" itemprop="image" src="/logo/Fillify-Logo.svg" alt="Fillify Logo" />
      <p class="p-note" itemprop="description">AI-Powered Forms, Emails & Bug Reports Assistant</p>
      
      <!-- h-product -->
      <div class="h-product">
        <span class="p-name">Fillify Chrome Extension</span>
        <span class="p-category">Business Tools</span>
        <span class="p-price">0</span>
        <data class="p-identifier" value="chrome-extension">Chrome Extension</data>
      </div>

      <!-- h-review-aggregate -->
      <div class="h-review-aggregate">
        <span class="p-item">Fillify</span>
        <span class="p-rating">4.8</span>
        <span class="p-count">100</span>
        <span class="p-category">Software</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style scoped>
.hidden {
  display: none;
}
</style> 