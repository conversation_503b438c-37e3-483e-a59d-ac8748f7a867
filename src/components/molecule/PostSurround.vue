<template>
  <nav class="post-navigation" aria-label="Post navigation">
    <div class="container">
      <h2 class="nav-title">{{ $t('blog.navigation.title') }}</h2>
      
      <div class="nav-links">
        <!-- Previous Post -->
        <div class="nav-item prev-post">
          <NuxtLink
            v-if="prev"
            :to="prev._path"
            class="nav-link"
            @click="handleNavClick('prev', prev)"
          >
            <div class="nav-direction">
              <Icon name="arrow-left" />
              <span class="nav-label">{{ $t('blog.navigation.previous') }}</span>
            </div>
            
            <div class="nav-content">
              <h3 class="nav-post-title">{{ prev.title }}</h3>
              <p v-if="prev.description" class="nav-post-excerpt">
                {{ prev.description }}
              </p>
              
              <div class="nav-meta">
                <time :datetime="prev.date">{{ formatDate(prev.date) }}</time>
                <span v-if="prev.readingTime">{{ prev.readingTime }} min read</span>
              </div>
            </div>
            
            <div v-if="prev.img" class="nav-image">
              <NuxtImg
                :src="`/images/blog/${prev.img}`"
                :alt="prev.alt || prev.title"
                loading="lazy"
              />
            </div>
          </NuxtLink>
          
          <div v-else class="nav-placeholder">
            <div class="placeholder-content">
              <Icon name="arrow-left" />
              <span>{{ $t('blog.navigation.noPrevious') }}</span>
            </div>
          </div>
        </div>
        
        <!-- Back to Blog -->
        <div class="nav-item back-to-blog">
          <NuxtLink to="/blog" class="back-link" @click="handleBackClick">
            <Icon name="grid" />
            <span>{{ $t('blog.navigation.backToBlog') }}</span>
          </NuxtLink>
        </div>
        
        <!-- Next Post -->
        <div class="nav-item next-post">
          <NuxtLink
            v-if="next"
            :to="next._path"
            class="nav-link"
            @click="handleNavClick('next', next)"
          >
            <div v-if="next.img" class="nav-image">
              <NuxtImg
                :src="`/images/blog/${next.img}`"
                :alt="next.alt || next.title"
                loading="lazy"
              />
            </div>
            
            <div class="nav-content">
              <h3 class="nav-post-title">{{ next.title }}</h3>
              <p v-if="next.description" class="nav-post-excerpt">
                {{ next.description }}
              </p>
              
              <div class="nav-meta">
                <time :datetime="next.date">{{ formatDate(next.date) }}</time>
                <span v-if="next.readingTime">{{ next.readingTime }} min read</span>
              </div>
            </div>
            
            <div class="nav-direction">
              <span class="nav-label">{{ $t('blog.navigation.next') }}</span>
              <Icon name="arrow-right" />
            </div>
          </NuxtLink>
          
          <div v-else class="nav-placeholder">
            <div class="placeholder-content">
              <span>{{ $t('blog.navigation.noNext') }}</span>
              <Icon name="arrow-right" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- Additional Navigation Options -->
      <div class="additional-nav">
        <button @click="scrollToTop" class="scroll-top-button">
          <Icon name="arrow-up" />
          {{ $t('blog.navigation.scrollTop') }}
        </button>
        
        <div class="share-buttons">
          <span class="share-label">{{ $t('blog.navigation.share') }}</span>
          <button
            v-for="platform in sharePlatforms"
            :key="platform.name"
            @click="sharePost(platform)"
            :class="['share-button', platform.name]"
            :title="`Share on ${platform.label}`"
          >
            <Icon :name="platform.icon" />
          </button>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import type { BlogPost, BlogNavigationProps } from '~/types/blog'

interface Props extends BlogNavigationProps {}

interface Emits {
  (e: 'nav-click', direction: 'prev' | 'next', post: BlogPost): void
  (e: 'back-click'): void
  (e: 'share', platform: string, url: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Get current route for sharing
const route = useRoute()
const currentUrl = computed(() => {
  if (process.client) {
    return window.location.href
  }
  return `https://fillify.tech${route.fullPath}`
})

// Share platforms configuration
const sharePlatforms = [
  {
    name: 'twitter',
    label: 'Twitter',
    icon: 'brand-twitter',
    getUrl: (url: string, title: string) => 
      `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`
  },
  {
    name: 'facebook',
    label: 'Facebook',
    icon: 'brand-facebook',
    getUrl: (url: string) => 
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`
  },
  {
    name: 'linkedin',
    label: 'LinkedIn',
    icon: 'brand-linkedin',
    getUrl: (url: string, title: string) => 
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`
  },
  {
    name: 'copy',
    label: 'Copy Link',
    icon: 'copy',
    getUrl: () => ''
  }
]

// Handle navigation clicks
const handleNavClick = (direction: 'prev' | 'next', post: BlogPost) => {
  emit('nav-click', direction, post)
}

const handleBackClick = () => {
  emit('back-click')
}

// Format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date)
}

// Scroll to top
const scrollToTop = () => {
  if (process.client) {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }
}

// Share post
const sharePost = async (platform: any) => {
  const url = currentUrl.value
  const title = document.title || 'Check out this blog post'
  
  if (platform.name === 'copy') {
    try {
      await navigator.clipboard.writeText(url)
      // You could show a toast notification here
      console.log('URL copied to clipboard')
    } catch (err) {
      console.error('Failed to copy URL:', err)
    }
  } else {
    const shareUrl = platform.getUrl(url, title)
    window.open(shareUrl, '_blank', 'width=600,height=400')
  }
  
  emit('share', platform.name, url)
}
</script>

<style scoped>
.post-navigation {
  margin-top: var(--space-16);
  padding-top: var(--space-8);
  border-top: 1px solid var(--border-primary);
  
  .container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
  }
}

.nav-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--space-8);
}

.nav-links {
  display: grid;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
  
  @media (min-width: 768px) {
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
  }
}

.nav-item {
  &.back-to-blog {
    @media (min-width: 768px) {
      display: flex;
      justify-content: center;
    }
  }
  
  &.next-post {
    @media (min-width: 768px) {
      text-align: right;
    }
  }
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  text-decoration: none;
  transition: all var(--transition-normal);
  
  &:hover {
    background: var(--bg-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
  
  .nav-direction {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    flex-shrink: 0;
    
    .nav-label {
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--text-tertiary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
  
  .nav-content {
    flex: 1;
    min-width: 0;
    
    .nav-post-title {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin-bottom: var(--space-2);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .nav-post-excerpt {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      margin-bottom: var(--space-3);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .nav-meta {
      font-size: var(--text-xs);
      color: var(--text-tertiary);
      display: flex;
      gap: var(--space-3);
    }
  }
  
  .nav-image {
    flex-shrink: 0;
    width: 80px;
    height: 60px;
    border-radius: var(--radius-md);
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.nav-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-6);
  background: var(--bg-secondary);
  border: 1px dashed var(--border-secondary);
  border-radius: var(--radius-lg);
  color: var(--text-tertiary);
  
  .placeholder-content {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
  }
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: var(--color-primary);
  color: var(--text-inverse);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-medium);
  transition: all var(--transition-fast);
  
  &:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

.additional-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-primary);
  
  @media (max-width: 767px) {
    flex-direction: column;
    gap: var(--space-4);
  }
}

.scroll-top-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  
  &:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
  }
}

.share-buttons {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  
  .share-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-right: var(--space-2);
  }
  
  .share-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    
    &:hover {
      transform: translateY(-1px);
    }
    
    &.twitter:hover {
      background: #1da1f2;
      color: white;
      border-color: #1da1f2;
    }
    
    &.facebook:hover {
      background: #4267b2;
      color: white;
      border-color: #4267b2;
    }
    
    &.linkedin:hover {
      background: #0077b5;
      color: white;
      border-color: #0077b5;
    }
    
    &.copy:hover {
      background: var(--color-primary);
      color: white;
      border-color: var(--color-primary);
    }
  }
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .nav-link {
    flex-direction: column;
    text-align: center;
    
    .nav-image {
      order: -1;
      width: 120px;
      height: 80px;
    }
    
    .nav-direction {
      order: 1;
    }
  }
  
  .nav-item.next-post .nav-link {
    .nav-direction {
      flex-direction: row-reverse;
    }
  }
}
</style>
