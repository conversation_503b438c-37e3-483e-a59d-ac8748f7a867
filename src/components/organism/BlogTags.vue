<template>
  <section class="blog-tags">
    <div class="container">
      <div class="tags-header">
        <h2 class="tags-title">{{ $t('blog.tags.title') }}</h2>
        <p class="tags-description">{{ $t('blog.tags.description') }}</p>
      </div>
      
      <div class="tags-content">
        <!-- All Posts Button -->
        <button
          :class="['tag-button', { active: !selectedTag }]"
          @click="handleTagClick('')"
        >
          <Icon name="grid" />
          {{ $t('blog.tags.all') }}
          <span v-if="totalPosts" class="tag-count">{{ totalPosts }}</span>
        </button>
        
        <!-- Tag Buttons -->
        <button
          v-for="tag in sortedTags"
          :key="tag.name"
          :class="['tag-button', { active: selectedTag === tag.name }]"
          @click="handleTagClick(tag.name)"
        >
          <Icon :name="getTagIcon(tag.name)" />
          {{ tag.name }}
          <span class="tag-count">{{ tag.count }}</span>
        </button>
      </div>
      
      <!-- Search Tags -->
      <div v-if="tags.length > 10" class="tags-search">
        <div class="search-input">
          <Icon name="search" />
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="$t('blog.tags.search')"
            @input="handleSearch"
          />
          <button
            v-if="searchQuery"
            @click="clearSearch"
            class="clear-button"
          >
            <Icon name="x" />
          </button>
        </div>
      </div>
      
      <!-- Popular Tags -->
      <div v-if="popularTags.length" class="popular-tags">
        <h3 class="popular-title">{{ $t('blog.tags.popular') }}</h3>
        <div class="popular-list">
          <button
            v-for="tag in popularTags"
            :key="tag.name"
            :class="['popular-tag', { active: selectedTag === tag.name }]"
            @click="handleTagClick(tag.name)"
          >
            {{ tag.name }}
            <span class="tag-count">{{ tag.count }}</span>
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import type { BlogTagsProps } from '~/types/blog'

interface TagWithCount {
  name: string
  count: number
}

interface Props extends BlogTagsProps {
  totalPosts?: number
  showSearch?: boolean
  showPopular?: boolean
  maxPopular?: number
}

interface Emits {
  (e: 'tag-selected', tag: string): void
  (e: 'search', query: string): void
}

const props = withDefaults(defineProps<Props>(), {
  tags: () => [],
  selectedTag: '',
  totalPosts: 0,
  showSearch: true,
  showPopular: true,
  maxPopular: 5
})

const emit = defineEmits<Emits>()

// Reactive state
const searchQuery = ref('')

// Compute tags with counts (mock implementation - in real app, this would come from props)
const tagsWithCounts = computed<TagWithCount[]>(() => {
  // In a real implementation, you would get tag counts from your data source
  return props.tags.map(tag => ({
    name: tag,
    count: Math.floor(Math.random() * 20) + 1 // Mock count
  }))
})

// Filter tags based on search
const filteredTags = computed(() => {
  if (!searchQuery.value) return tagsWithCounts.value
  
  const query = searchQuery.value.toLowerCase()
  return tagsWithCounts.value.filter(tag =>
    tag.name.toLowerCase().includes(query)
  )
})

// Sort tags alphabetically
const sortedTags = computed(() => {
  return [...filteredTags.value].sort((a, b) => a.name.localeCompare(b.name))
})

// Get popular tags (sorted by count)
const popularTags = computed(() => {
  if (!props.showPopular) return []
  
  return [...tagsWithCounts.value]
    .sort((a, b) => b.count - a.count)
    .slice(0, props.maxPopular)
})

// Handle tag click
const handleTagClick = (tag: string) => {
  emit('tag-selected', tag)
}

// Handle search
const handleSearch = () => {
  emit('search', searchQuery.value)
}

// Clear search
const clearSearch = () => {
  searchQuery.value = ''
  emit('search', '')
}

// Get icon for tag (you can customize this based on tag names)
const getTagIcon = (tagName: string) => {
  const iconMap: Record<string, string> = {
    'javascript': 'brand-javascript',
    'typescript': 'brand-typescript',
    'vue': 'brand-vue',
    'nuxt': 'brand-nuxt',
    'react': 'brand-react',
    'node': 'brand-nodejs',
    'css': 'brand-css3',
    'html': 'brand-html5',
    'design': 'palette',
    'tutorial': 'book',
    'tips': 'bulb',
    'news': 'news',
    'ai': 'robot',
    'automation': 'settings',
    'forms': 'forms',
    'productivity': 'trending-up',
    'tools': 'tool',
    'web': 'world',
    'mobile': 'device-mobile',
    'api': 'api',
    'database': 'database',
    'security': 'shield',
    'performance': 'speedometer',
    'testing': 'test-pipe',
    'deployment': 'rocket',
    'devops': 'server'
  }
  
  return iconMap[tagName.toLowerCase()] || 'tag'
}

// Watch for external tag changes
watch(() => props.selectedTag, (newTag) => {
  // You can add any side effects here when the selected tag changes
})
</script>

<style scoped>
.blog-tags {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-8) 0;
  
  .container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
  }
}

.tags-header {
  text-align: center;
  margin-bottom: var(--space-8);
  
  .tags-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-3);
    
    @media (min-width: 768px) {
      font-size: var(--text-3xl);
    }
  }
  
  .tags-description {
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
  }
}

.tags-content {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  justify-content: center;
  margin-bottom: var(--space-8);
  
  @media (min-width: 768px) {
    justify-content: flex-start;
  }
}

.tag-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  
  &:hover {
    background: var(--color-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);
    transform: translateY(-1px);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
  
  &.active {
    background: var(--color-primary);
    color: var(--text-inverse);
    border-color: var(--color-primary);
  }
  
  .tag-count {
    font-size: var(--text-xs);
    background: rgba(255, 255, 255, 0.2);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    min-width: 20px;
    text-align: center;
  }
  
  &:not(.active) .tag-count {
    background: var(--bg-secondary);
    color: var(--text-tertiary);
  }
}

.tags-search {
  margin-bottom: var(--space-8);
  
  .search-input {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
    
    input {
      width: 100%;
      padding: var(--space-3) var(--space-12) var(--space-3) var(--space-10);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-md);
      background: var(--bg-primary);
      color: var(--text-primary);
      font-size: var(--text-base);
      transition: border-color var(--transition-fast);
      
      &:focus {
        outline: none;
        border-color: var(--color-primary);
        box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
      }
      
      &::placeholder {
        color: var(--text-tertiary);
      }
    }
    
    > :first-child {
      position: absolute;
      left: var(--space-3);
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-tertiary);
      pointer-events: none;
    }
    
    .clear-button {
      position: absolute;
      right: var(--space-3);
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: var(--text-tertiary);
      cursor: pointer;
      padding: var(--space-1);
      border-radius: var(--radius-sm);
      transition: color var(--transition-fast);
      
      &:hover {
        color: var(--text-secondary);
      }
    }
  }
}

.popular-tags {
  text-align: center;
  
  .popular-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
  }
  
  .popular-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
    justify-content: center;
    
    @media (min-width: 768px) {
      justify-content: flex-start;
    }
  }
  
  .popular-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    
    &:hover {
      background: var(--color-secondary);
      color: var(--text-inverse);
      border-color: var(--color-secondary);
    }
    
    &.active {
      background: var(--color-secondary);
      color: var(--text-inverse);
      border-color: var(--color-secondary);
    }
    
    .tag-count {
      font-size: var(--text-xs);
      background: rgba(255, 255, 255, 0.2);
      padding: 2px var(--space-1);
      border-radius: var(--radius-sm);
      min-width: 16px;
      text-align: center;
    }
    
    &:not(.active) .tag-count {
      background: var(--bg-secondary);
      color: var(--text-tertiary);
    }
  }
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .blog-tags {
    padding: var(--space-6) 0;
  }
  
  .tags-header {
    margin-bottom: var(--space-6);
    
    .tags-title {
      font-size: var(--text-xl);
    }
  }
  
  .tags-content {
    margin-bottom: var(--space-6);
  }
  
  .tags-search {
    margin-bottom: var(--space-6);
  }
}

/* Animation for tags appearing */
.tag-button,
.popular-tag {
  animation: fadeInScale 0.3s ease-out;
  animation-fill-mode: both;
}

.tag-button:nth-child(1) { animation-delay: 0.05s; }
.tag-button:nth-child(2) { animation-delay: 0.1s; }
.tag-button:nth-child(3) { animation-delay: 0.15s; }
.tag-button:nth-child(4) { animation-delay: 0.2s; }
.tag-button:nth-child(5) { animation-delay: 0.25s; }

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
