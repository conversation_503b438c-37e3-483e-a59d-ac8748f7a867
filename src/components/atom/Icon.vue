<template>
  <span :class="['icon', `icon-${name}`, sizeClass]" :style="iconStyle">
    <!-- Fallback to text if icon not found -->
    {{ iconText }}
  </span>
</template>

<script setup lang="ts">
interface Props {
  name: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md'
})

// Size classes
const sizeClass = computed(() => `size-${props.size}`)

// Icon style
const iconStyle = computed(() => ({
  color: props.color || undefined
}))

// Icon text mapping (fallback for when actual icons aren't available)
const iconMap: Record<string, string> = {
  // Navigation
  'arrow-left': '←',
  'arrow-right': '→',
  'arrow-up': '↑',
  'arrow-down': '↓',
  
  // UI
  'calendar': '📅',
  'clock': '⏰',
  'user': '👤',
  'tag': '🏷️',
  'grid': '⊞',
  'search': '🔍',
  'x': '✕',
  'copy': '📋',
  
  // Social
  'brand-twitter': '🐦',
  'brand-facebook': '📘',
  'brand-linkedin': '💼',
  'brand-javascript': 'JS',
  'brand-typescript': 'TS',
  'brand-vue': 'V',
  'brand-nuxt': 'N',
  'brand-react': 'R',
  'brand-nodejs': 'Node',
  'brand-css3': 'CSS',
  'brand-html5': 'HTML',
  
  // Content
  'palette': '🎨',
  'book': '📚',
  'bulb': '💡',
  'news': '📰',
  'robot': '🤖',
  'settings': '⚙️',
  'forms': '📝',
  'trending-up': '📈',
  'tool': '🔧',
  'world': '🌐',
  'device-mobile': '📱',
  'api': '🔌',
  'database': '🗄️',
  'shield': '🛡️',
  'speedometer': '⚡',
  'test-pipe': '🧪',
  'rocket': '🚀',
  'server': '🖥️'
}

const iconText = computed(() => iconMap[props.name] || '•')
</script>

<style scoped>
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-style: normal;
  line-height: 1;
  vertical-align: middle;
}

.size-xs {
  font-size: 0.75rem;
  width: 0.75rem;
  height: 0.75rem;
}

.size-sm {
  font-size: 0.875rem;
  width: 0.875rem;
  height: 0.875rem;
}

.size-md {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

.size-lg {
  font-size: 1.25rem;
  width: 1.25rem;
  height: 1.25rem;
}

.size-xl {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}
</style>
