<template>
  <div class="blog-list">
    <div class="container">
      <!-- Loading State -->
      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>{{ $t('blog.loading') }}</p>
      </div>
      
      <!-- Error State -->
      <div v-else-if="error" class="error-state">
        <div class="error-icon">⚠️</div>
        <h3>{{ $t('blog.error.title') }}</h3>
        <p>{{ $t('blog.error.description') }}</p>
        <button @click="$emit('retry')" class="retry-button">
          {{ $t('blog.error.retry') }}
        </button>
      </div>
      
      <!-- Empty State -->
      <div v-else-if="!posts || posts.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <h3>{{ $t('blog.empty.title') }}</h3>
        <p>{{ $t('blog.empty.description') }}</p>
      </div>
      
      <!-- Posts Grid -->
      <div v-else class="posts-grid">
        <TemplateBlogPost
          v-for="post in posts"
          :key="post._id"
          :post="post"
          mode="card"
          @click="handlePostClick(post)"
        />
      </div>
      
      <!-- Load More Button -->
      <div v-if="hasMore" class="load-more">
        <button 
          @click="$emit('load-more')"
          :disabled="loadingMore"
          class="load-more-button"
        >
          <span v-if="loadingMore" class="loading-spinner"></span>
          {{ loadingMore ? $t('blog.loadingMore') : $t('blog.loadMore') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { BlogPost, BlogListProps } from '~/types/blog'

interface Props extends BlogListProps {
  hasMore?: boolean
  loadingMore?: boolean
}

interface Emits {
  (e: 'post-click', post: BlogPost): void
  (e: 'load-more'): void
  (e: 'retry'): void
}

const props = withDefaults(defineProps<Props>(), {
  posts: () => [],
  loading: false,
  hasMore: false,
  loadingMore: false
})

const emit = defineEmits<Emits>()

// Handle post click
const handlePostClick = (post: BlogPost) => {
  emit('post-click', post)
  
  // Navigate to post
  const router = useRouter()
  router.push(post._path)
}

// Computed properties
const isEmpty = computed(() => !props.posts || props.posts.length === 0)
const hasError = computed(() => !!props.error)
const isLoading = computed(() => props.loading)
</script>

<style scoped>
.blog-list {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
}

.posts-grid {
  display: grid;
  gap: var(--space-8);
  
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16) var(--space-4);
  text-align: center;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-primary);
    border-top-color: var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-4);
  }
  
  p {
    color: var(--text-secondary);
    font-size: var(--text-lg);
  }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16) var(--space-4);
  text-align: center;
  
  .error-icon {
    font-size: 4rem;
    margin-bottom: var(--space-6);
  }
  
  h3 {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
  }
  
  p {
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
    max-width: 400px;
  }
  
  .retry-button {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    background: var(--color-primary);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: background var(--transition-fast);
    
    &:hover {
      background: var(--color-primary-dark);
    }
    
    &:focus {
      outline: 2px solid var(--color-primary);
      outline-offset: 2px;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16) var(--space-4);
  text-align: center;
  
  .empty-icon {
    font-size: 4rem;
    margin-bottom: var(--space-6);
    opacity: 0.5;
  }
  
  h3 {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
  }
  
  p {
    color: var(--text-secondary);
    max-width: 400px;
  }
}

.load-more {
  display: flex;
  justify-content: center;
  margin-top: var(--space-12);
  
  .load-more-button {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-8);
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    
    &:hover:not(:disabled) {
      background: var(--bg-tertiary);
      transform: translateY(-1px);
    }
    
    &:focus {
      outline: 2px solid var(--color-primary);
      outline-offset: 2px;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid var(--border-primary);
      border-top-color: var(--color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 639px) {
  .posts-grid {
    grid-template-columns: 1fr;
  }
}

/* Animation for posts appearing */
.posts-grid > * {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.posts-grid > *:nth-child(1) { animation-delay: 0.1s; }
.posts-grid > *:nth-child(2) { animation-delay: 0.2s; }
.posts-grid > *:nth-child(3) { animation-delay: 0.3s; }
.posts-grid > *:nth-child(4) { animation-delay: 0.4s; }
.posts-grid > *:nth-child(5) { animation-delay: 0.5s; }
.posts-grid > *:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
