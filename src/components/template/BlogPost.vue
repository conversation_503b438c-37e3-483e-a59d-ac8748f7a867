<template>
  <article :class="['blog-post-template', `mode-${mode}`]">
    <!-- Card Mode -->
    <div v-if="mode === 'card'" class="blog-post-card" @click="handleClick">
      <div v-if="post.img" class="post-image">
        <NuxtImg
          :src="`/images/blog/${post.img}`"
          :alt="post.alt || post.title"
          :loading="imageLoading"
          @load="handleImageLoad"
          @error="handleImageError"
        />
        <div v-if="imageLoading" class="image-placeholder">
          <div class="image-spinner"></div>
        </div>
      </div>
      
      <div class="post-content">
        <h3 class="post-title">
          <NuxtLink :to="post._path">{{ post.title }}</NuxtLink>
        </h3>
        
        <p v-if="post.description" class="post-excerpt">
          {{ post.description }}
        </p>
        
        <div class="post-meta">
          <div class="post-date">
            <Icon name="calendar" />
            <time :datetime="post.date">{{ formatDate(post.date) }}</time>
          </div>
          
          <div v-if="post.readingTime" class="reading-time">
            <Icon name="clock" />
            <span>{{ post.readingTime }} min read</span>
          </div>
        </div>
        
        <div v-if="post.tags && post.tags.length" class="post-tags">
          <span
            v-for="tag in post.tags.slice(0, 3)"
            :key="tag"
            class="tag"
          >
            {{ tag }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- List Mode -->
    <div v-else-if="mode === 'list'" class="blog-post-list" @click="handleClick">
      <div v-if="post.img" class="post-image">
        <NuxtImg
          :src="`/images/blog/${post.img}`"
          :alt="post.alt || post.title"
          :loading="imageLoading"
        />
      </div>
      
      <div class="post-content">
        <h3 class="post-title">
          <NuxtLink :to="post._path">{{ post.title }}</NuxtLink>
        </h3>
        
        <p v-if="post.description" class="post-excerpt">
          {{ post.description }}
        </p>
        
        <div class="post-meta">
          <time :datetime="post.date">{{ formatDate(post.date) }}</time>
          <span v-if="post.readingTime">{{ post.readingTime }} min read</span>
        </div>
      </div>
    </div>
    
    <!-- Page Mode -->
    <div v-else-if="mode === 'page'" class="blog-post-page">
      <header class="post-header">
        <h1 class="post-title">{{ post.title }}</h1>
        
        <div class="post-meta">
          <div class="meta-item">
            <Icon name="calendar" />
            <time :datetime="post.date">{{ formatDate(post.date) }}</time>
          </div>
          
          <div v-if="post.readingTime" class="meta-item">
            <Icon name="clock" />
            <span>{{ post.readingTime }} min read</span>
          </div>
          
          <div v-if="post.author" class="meta-item">
            <Icon name="user" />
            <span>{{ post.author }}</span>
          </div>
        </div>
        
        <div v-if="post.img" class="post-image">
          <NuxtImg
            :src="`/images/blog/${post.img}`"
            :alt="post.alt || post.title"
            loading="eager"
          />
        </div>
      </header>
      
      <div class="post-content">
        <ContentRenderer :value="post" />
      </div>
      
      <footer v-if="post.tags && post.tags.length" class="post-footer">
        <div class="post-tags">
          <h4 class="tags-title">{{ $t('blog.post.tags') }}</h4>
          <div class="tags-list">
            <NuxtLink
              v-for="tag in post.tags"
              :key="tag"
              :to="`/blog?tag=${encodeURIComponent(tag)}`"
              class="tag"
            >
              {{ tag }}
            </NuxtLink>
          </div>
        </div>
      </footer>
    </div>
  </article>
</template>

<script setup lang="ts">
import type { BlogPost, BlogPostProps } from '~/types/blog'

interface Props extends BlogPostProps {
  imageLoading?: 'lazy' | 'eager'
}

interface Emits {
  (e: 'click', post: BlogPost): void
  (e: 'tag-click', tag: string): void
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'card',
  imageLoading: 'lazy'
})

const emit = defineEmits<Emits>()

// Image loading state
const imageLoading = ref(true)

// Handle post click
const handleClick = () => {
  if (props.mode !== 'page') {
    emit('click', props.post)
  }
}

// Handle image events
const handleImageLoad = () => {
  imageLoading.value = false
}

const handleImageError = () => {
  imageLoading.value = false
  console.warn(`Failed to load image: ${props.post.img}`)
}

// Format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date)
}

// Calculate reading time if not provided
const calculateReadingTime = (content: string) => {
  const wordsPerMinute = 200
  const words = content.trim().split(/\s+/).length
  return Math.ceil(words / wordsPerMinute)
}

// Computed reading time
const readingTime = computed(() => {
  if (props.post.readingTime) {
    return props.post.readingTime
  }
  
  if (props.post.body && typeof props.post.body === 'string') {
    return calculateReadingTime(props.post.body)
  }
  
  return null
})
</script>

<style scoped>
.blog-post-template {
  width: 100%;
}

/* Card Mode Styles */
.blog-post-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }
  
  .post-image {
    position: relative;
    aspect-ratio: 16 / 9;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform var(--transition-slow);
    }
    
    .image-placeholder {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-secondary);
      
      .image-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid var(--border-primary);
        border-top-color: var(--color-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
  
  &:hover .post-image img {
    transform: scale(1.05);
  }
  
  .post-content {
    padding: var(--space-6);
  }
  
  .post-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-3);
    
    a {
      color: var(--text-primary);
      text-decoration: none;
      
      &:hover {
        color: var(--color-primary);
      }
    }
  }
  
  .post-excerpt {
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .post-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    margin-bottom: var(--space-4);
    
    .post-date,
    .reading-time {
      display: flex;
      align-items: center;
      gap: var(--space-1);
    }
  }
  
  .post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
    
    .tag {
      font-size: var(--text-xs);
      font-weight: var(--font-medium);
      background: var(--bg-secondary);
      color: var(--color-primary);
      padding: var(--space-1) var(--space-2);
      border-radius: var(--radius-sm);
    }
  }
}

/* List Mode Styles */
.blog-post-list {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  
  &:hover {
    background: var(--bg-secondary);
  }
  
  .post-image {
    flex-shrink: 0;
    width: 120px;
    aspect-ratio: 16 / 9;
    border-radius: var(--radius-md);
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .post-content {
    flex: 1;
    
    .post-title {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      margin-bottom: var(--space-2);
      
      a {
        color: var(--text-primary);
        text-decoration: none;
        
        &:hover {
          color: var(--color-primary);
        }
      }
    }
    
    .post-excerpt {
      color: var(--text-secondary);
      font-size: var(--text-sm);
      line-height: var(--leading-normal);
      margin-bottom: var(--space-3);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .post-meta {
      font-size: var(--text-xs);
      color: var(--text-tertiary);
      display: flex;
      gap: var(--space-3);
    }
  }
}

/* Page Mode Styles */
.blog-post-page {
  max-width: 800px;
  margin: 0 auto;
  
  .post-header {
    text-align: center;
    margin-bottom: var(--space-12);
    
    .post-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      line-height: var(--leading-tight);
      color: var(--text-primary);
      margin-bottom: var(--space-6);
      
      @media (min-width: 768px) {
        font-size: var(--text-5xl);
      }
    }
    
    .post-meta {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--space-4);
      font-size: var(--text-base);
      color: var(--text-secondary);
      margin-bottom: var(--space-8);
      
      @media (max-width: 767px) {
        flex-direction: column;
        gap: var(--space-2);
      }
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: var(--space-1);
      }
    }
    
    .post-image {
      aspect-ratio: 16 / 9;
      border-radius: var(--radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  
  .post-content {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--text-secondary);
    
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
      color: var(--text-primary);
      margin-top: var(--space-12);
      margin-bottom: var(--space-6);
    }
    
    :deep(p) {
      margin-bottom: var(--space-6);
    }
    
    :deep(ul),
    :deep(ol) {
      margin-bottom: var(--space-6);
      padding-left: var(--space-6);
      
      li {
        margin-bottom: var(--space-3);
      }
    }
    
    :deep(blockquote) {
      margin: var(--space-8) 0;
      padding: var(--space-6);
      background: var(--bg-secondary);
      border-left: 4px solid var(--color-primary);
      border-radius: var(--radius-md);
      font-style: italic;
    }
    
    :deep(pre) {
      margin: var(--space-8) 0;
      border-radius: var(--radius-md);
      overflow-x: auto;
    }
    
    :deep(img) {
      margin: var(--space-8) auto;
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-md);
    }
  }
  
  .post-footer {
    margin-top: var(--space-12);
    padding-top: var(--space-8);
    border-top: 1px solid var(--border-primary);
    
    .post-tags {
      .tags-title {
        font-size: var(--text-base);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin-bottom: var(--space-4);
      }
      
      .tags-list {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-3);
        
        .tag {
          display: inline-flex;
          align-items: center;
          gap: var(--space-2);
          padding: var(--space-2) var(--space-4);
          background: var(--bg-secondary);
          color: var(--text-primary);
          border: 1px solid var(--border-primary);
          border-radius: var(--radius-full);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          text-decoration: none;
          transition: all var(--transition-fast);
          
          &:hover {
            background: var(--color-primary);
            color: var(--text-inverse);
            border-color: var(--color-primary);
          }
        }
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
