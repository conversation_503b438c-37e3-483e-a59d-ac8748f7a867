import { useI18n } from 'vue-i18n'
import { watch, ref } from 'vue'

export const useSeo = () => {
  const { t, locale, te } = useI18n()
  const currentPath = ref('')
  
  const setSeoMeta = (path: string = '') => {
    currentPath.value = path
    updateSeoMeta()
  }

  const updateSeoMeta = () => {
    const path = currentPath.value
    // Get base meta information
    // Convert path like "/privacy" to "privacy" for translation keys
    const translationPath = path ? `${path.replace(/^\//, '').replace(/\//g, '.')}` : ''
    const titleKey = translationPath ? `${translationPath}.meta.title` : 'meta.title'
    const descriptionKey = translationPath ? `${translationPath}.meta.description` : 'meta.description'
    const title = t(titleKey)
    const description = t(descriptionKey)
    
    // Get keywords from translations
    const mainKeywords = [
      t('meta.keywords.formFilling'),
      t('meta.keywords.automation'),
      t('meta.keywords.email'),
      t('meta.keywords.bugReport')
    ]
    
    // Get additional keywords from translations with fallback
    const hasAdditionalKeywords = te('meta.keywords.additional')
    const additionalKeywords = hasAdditionalKeywords ? 
      Array.from({ length: 10 }, (_, i) => t(`meta.keywords.additional.${i}`)).filter(Boolean) : 
      []
    
    const keywords = path === '' ? [...mainKeywords, ...additionalKeywords] : []

    // Schema.org JSON-LD
    const schemaOrgData = {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: 'Fillify',
      applicationCategory: 'BusinessApplication',
      operatingSystem: 'Chrome',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD'
      },
      description,
      url: `https://fillify.tech${path}`,
      author: {
        '@type': 'Organization',
        name: 'Fillify Team'
      },
      browserRequirements: 'Requires Chrome Browser',
      softwareVersion: '1.0',
      keywords: keywords.join(', '),
      image: 'https://fillify.tech/og/og-image.webp',
      thumbnailUrl: 'https://fillify.tech/og/og-image.webp',
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: '4.8',
        ratingCount: '100'
      }
    }

    // Set basic meta tags
    useHead({
      title,
      htmlAttrs: {
        lang: locale.value
      },
      link: [
        {
          rel: 'canonical',
          href: `https://fillify.tech${path}`
        },
        // 为每个语言版本添加hreflang标签
        {
          rel: 'alternate',
          hreflang: 'x-default',
          href: path ? `https://fillify.tech${path.startsWith('/') ? path : '/' + path}` : 'https://fillify.tech/'
        },
        {
          rel: 'alternate',
          hreflang: 'en',
          href: path ? `https://fillify.tech/en${path.startsWith('/') ? path : '/' + path}` : 'https://fillify.tech/en/'
        },
        {
          rel: 'alternate',
          hreflang: 'es',
          href: path ? `https://fillify.tech/es${path.startsWith('/') ? path : '/' + path}` : 'https://fillify.tech/es/'
        },
        {
          rel: 'alternate',
          hreflang: 'fr',
          href: path ? `https://fillify.tech/fr${path.startsWith('/') ? path : '/' + path}` : 'https://fillify.tech/fr/'
        },
        {
          rel: 'alternate',
          hreflang: 'ja',
          href: path ? `https://fillify.tech/ja${path.startsWith('/') ? path : '/' + path}` : 'https://fillify.tech/ja/'
        },
        {
          rel: 'alternate',
          hreflang: 'ko',
          href: path ? `https://fillify.tech/ko${path.startsWith('/') ? path : '/' + path}` : 'https://fillify.tech/ko/'
        },
        {
          rel: 'alternate',
          hreflang: 'zh',
          href: path ? `https://fillify.tech/zh${path.startsWith('/') ? path : '/' + path}` : 'https://fillify.tech/zh/'
        },
        {
          rel: 'alternate',
          hreflang: 'zh-TW',
          href: path ? `https://fillify.tech/zh-TW${path.startsWith('/') ? path : '/' + path}` : 'https://fillify.tech/zh-TW/'
        },
        {
          rel: 'alternate',
          hreflang: 'ru',
          href: path ? `https://fillify.tech/ru${path.startsWith('/') ? path : '/' + path}` : 'https://fillify.tech/ru/'
        }
      ],
      meta: [
        {
          property: 'og:image',
          content: 'https://fillify.tech/og/og-image.webp'
        },
        {
          property: 'og:image:type',
          content: 'image/webp'
        },
        {
          property: 'og:image:width',
          content: '1200'
        },
        {
          property: 'og:image:height',
          content: '630'
        },
        {
          property: 'og:image:alt',
          content: 'Fillify - AI-Powered Form Automation Platform'
        },
        // PNG 作为后备
        {
          property: 'og:image:fallback',
          content: 'https://fillify.tech/og/og-image.png'
        }
      ],
      script: [
        {
          type: 'application/ld+json',
          children: JSON.stringify(schemaOrgData)
        }
      ]
    })

    // Set SEO meta tags
    useSeoMeta({
      title,
      description,
      keywords: keywords.join(', '),
      
      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogUrl: `https://fillify.tech${path}`,
      ogType: 'website',
      ogSiteName: 'Fillify',
      
      // Twitter (直接使用 PNG，因为不支持 WebP)
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: {
        url: 'https://fillify.tech/og/og-image.png',
        type: 'image/png'
      },
      twitterCard: 'summary_large_image',
      
      // Additional meta
      applicationName: 'Fillify',
      author: 'Fillify Team',
      robots: 'index, follow',
      viewport: 'width=device-width, initial-scale=1, maximum-scale=1'
    })
  }

  // Watch for language changes
  watch(locale, () => {
    if (currentPath.value !== undefined) {
      updateSeoMeta()
    }
  })

  return {
    setSeoMeta
  }
}