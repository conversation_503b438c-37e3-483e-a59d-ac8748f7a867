export default {
  $locale: {
    name: 'Russian',
    nativeName: 'Русский'
  },
  welcome: 'Добро пожаловать в Fillify',
  description: 'Заполнение форм с использованием ИИ',
  nav: {
    home: 'Главная',
    signin: 'Войт<PERSON>',
    dashboard: 'Панель управления',
    signout: 'Выйти',
    startFree: 'Начать бесплатно',
    language: 'Язык'
  },
  hero: {
    chromeStore: 'Теперь доступно в Chrome Web Store',
    title: {
      text: 'Преобразите заполнение форм с помощью ',
      rotatingWords: [
        'магии на основе ИИ',
        'умной автоматизации',
        'технологий будущего',
        'идеальной точности',
        'бесшовной интеграции'
      ]
    },
    description: 'Просто напишите одно предложение, и ИИ мгновенно заполнит любую веб-форму. Умнейший способ обработки онлайн-форм.',
    cta: {
      chrome: 'Добавить в Chrome',
      learnMore: 'Узнать больше'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: 'Формы, заполняемые ежедневно'
    },
    accuracy: {
      value: '%',
      label: 'Точность'
    },
    support: {
      value: '24/7',
      label: 'Поддержка ИИ'
    }
  },
  features: {
    title: 'Испытайте мощь ИИ',
    subtitle: 'Узнайте, как Fillify трансформирует ваш ежедневный рабочий процесс с помощью интеллектуальной автоматизации',
    formFilling: {
      title: 'Универсальное заполнение форм',
      description: 'Заполняйте любые веб-формы с точностью ИИ. От простых регистрационных форм до сложных заявок — Fillify преобразует ваши текстовые описания в точные данные форм, экономя часы ручной работы.',
      alt: 'Скриншот, показывающий, как Fillify автоматически заполняет веб-форму с помощью ИИ'
    },
    email: {
      title: 'Умный помощник для писем',
      description: 'Мгновенно создавайте профессиональные письма с нашим помощником на базе ИИ. Поддержка Gmail и Outlook позволяет преобразовывать ваши краткие описания в хорошо структурированные письма, делая написание писем простым.',
      alt: 'Демонстрация составления письма в Fillify с помощью ИИ'
    },
    bugReport: {
      title: 'Интеллектуальные отчеты о багах',
      description: 'Создавайте подробные отчеты о багах одним кликом. Поддержка GitHub Issues, JIRA и других платформ. Наш ИИ преобразует ваши краткие описания в детальные, хорошо структурированные отчеты, помогая вашей команде эффективно общаться.',
      alt: 'Пример создания подробного отчета о баге для GitHub Issues в Fillify'
    },
    aiProvider: {
      title: 'Выберите своего поставщика ИИ',
      description: 'Свобода выбора предпочитаемого сервиса ИИ. Будь то OpenAI, Anthropic Claude или другие — просто введите свой API-ключ и начинайте использовать модель ИИ по вашему выбору. Полный контроль над вашим опытом работы с ИИ и легкость смены провайдера.',
      alt: 'Интерфейс выбора различных поставщиков ИИ в Fillify'
    }
  },
  faq: {
    title: 'Часто задаваемые вопросы',
    items: {
      what: {
        question: 'Что такое Fillify?',
        answer: 'Fillify — это расширение для Chrome, использующее ИИ для мгновенного заполнения веб-форм на основе одного предложения. Будь то формы регистрации, отчеты о багах или письма, Fillify понимает ваше намерение и заполняет формы точно и умно.'
      },
      types: {
        question: 'Какие формы может обрабатывать Fillify?',
        answer: 'Fillify работает с различными формами, включая общие веб-формы, отчеты о багах и письма. Оно поддерживает текстовые поля, текстовые области и другие элементы, обеспечивая плавную автоматизацию на разных вебсайтах.'
      },
      providers: {
        question: 'Каких поставщиков ИИ поддерживает Fillify?',
        answer: 'Fillify интегрируется с несколькими поставщиками ИИ, включая OpenAI, Anthropic Claude, Google Gemini и Moonshot AI. Вы можете легко переключаться между поставщиками и использовать собственные API-ключи для максимальной гибкости и контроля.'
      },
      privacy: {
        question: 'Как Fillify защищает мои данные и конфиденциальность?',
        answer: "Мы очень серьезно относимся к вашей конфиденциальности и безопасности данных. Ваш API-ключ хранится локально в браузере и передается на наш сервер только при запросе, где он шифруется перед отправкой выбранному вами поставщику ИИ. Мы не храним, не используем в других целях и не передаем ваш API-ключ третьим лицам, гарантируя безопасность ваших данных."
      },
      customize: {
        question: 'Могу ли я настроить ответы ИИ для определенных форм?',
        answer: 'Да! В режиме отчетов о багах вы можете создать собственные шаблоны с предустановленной информацией, чтобы генерировать более точные и последовательные отчеты.'
      },
      languages: {
        question: 'Какие языки поддерживает Fillify?',
        answer: 'Fillify поддерживает несколько языков и может автоматически определять язык формы. Вы также можете вручную выбрать предпочитаемый язык вывода в всплывающем окне расширения.'
      }
    }
  },
  bottomCta: {
    subtitle: 'Готовы изменить свой рабочий процесс?',
    title: 'Испытайте будущее заполнения форм уже сегодня',
    button: 'Установить сейчас'
  },
  footer: {
    copyright: '© {year} Fillify. Все права защищены.',
    social: {
      twitter: 'X (Twitter)',
      youtube: 'YouTube'
    },
    links: {
      terms: 'Условия обслуживания',
      privacy: 'Политика конфиденциальности'
    }
  },
  signin: {
    title: 'Добро пожаловать в Fillify',
    subtitle: 'Войдите, чтобы использовать наше расширение для заполнения форм на базе ИИ',
    features: {
      title: 'Что вы получите:',
      list: {
        autoFill: 'Автозаполнение форм с использованием ИИ',
        api: 'Настройка с вашим собственным API',
        early: 'Ранний доступ к новым функциям'
      }
    },
    terms: {
      prefix: 'Войдя, вы соглашаетесь с нашими',
      and: 'и'
    },
    seo: {
      title: 'Войти - Fillify',
      description: 'Войдите в Fillify, чтобы получить доступ к функциям автозаполнения форм на основе ИИ'
    }
  },
  meta: {
    title: 'Fillify - ИИ-помощник для форм, писем и отчетов о багах',
    description: 'Fillify революционизирует заполнение форм с помощью технологий ИИ. Автоматически заполняйте веб-формы, составляйте письма и создавайте отчеты о багах с помощью интеллектуальной автоматизации.',
    keywords: {
      formFilling: 'Заполнение форм с ИИ',
      automation: 'Автоматизация с ИИ',
      email: 'Создание писем с ИИ',
      bugReport: 'Генерация отчетов о багах с ИИ',
      additional: [
        'Умное завершение форм',
        'Автоматический ввод данных',
        'Помощник для форм на ИИ',
        'Интеллектуальное заполнение форм',
        'Автозаполнение форм в Chrome',
        'AI заполнитель форм',
        'Заполнение форм ИИ',
        'Автозаполнение форм',
        'Онлайн заполнитель форм',
        'Автоматизация веб-форм',
        'Ввод данных с ИИ',
        'Автоматическое заполнение форм',
        'Расширение Chrome с ИИ',
        'Процессор форм ИИ',
        'Цифровая автоматизация форм',
        'Браузерный заполнитель форм',
        'Инструмент для форм ИИ',
        'Веб-формы ИИ',
        'Программное обеспечение для автоматизации форм',
        'Решение для форм с ИИ'
      ]
    }
  },
  privacy: {
    meta: {
      title: 'Политика конфиденциальности - Fillify',
      description: 'Узнайте, как Fillify защищает вашу конфиденциальность и обрабатывает ваши данные.'
    },
    title: 'Политика конфиденциальности',
    lastUpdated: 'Последнее обновление: {date}'
  },
  terms: {
    meta: {
      title: 'Условия обслуживания - Fillify',
      description: 'Прочитайте об условиях и правилах использования услуг Fillify.'
    },
    title: 'Условия обслуживания',
    lastUpdated: 'Последнее обновление: {date}'
  },
  dashboard: {
    meta: {
      title: 'Панель управления - Fillify',
      description: 'Управляйте своим аккаунтом Fillify, просматривайте текущий план и отслеживайте использование.'
    },
    currentPlan: 'Текущий план',
    settings: 'Настройки',
    usageOverview: 'Обзор использования',
    creditsUsed: 'Использованные кредиты'
  },
  '404': {
    title: 'Страница не найдена',
    description: 'Извините, мы не нашли страницу, которую вы ищете. Пожалуйста, проверьте URL или вернитесь на главную страницу.',
    backHome: 'Вернуться на главную'
  }
}