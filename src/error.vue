<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()

// 获取错误信息
const props = defineProps({
  error: Object
})

const goHome = () => {
  // 清除错误并返回首页
  clearError()
  router.push('/')
}

// 处理错误信息显示
const errorMessage = computed(() => {
  if (!props.error) return t('404.description')
  // 如果是 404 错误，使用我们的翻译
  if (props.error.statusCode === 404) return t('404.description')
  // 其他错误使用错误信息或默认翻译
  return props.error.message || t('404.description')
})

// SEO
useSeoMeta({
  title: () => `404 - ${t('404.title')} | Fillify`,
  description: () => t('404.description'),
  ogDescription: () => t('404.description'),
  ogTitle: () => `404 - ${t('404.title')} | Fillify`,
})
</script>

<template>
  <div class="min-h-screen relative">
    <!-- Background Color -->
    <div class="absolute inset-0 bg-gradient-to-b from-blue-50 to-white"></div>
    
    <!-- Main Content Wrapper -->
    <div class="relative">
      <!-- Hero Background Grid -->
      <div class="absolute inset-0 -mx-4" style="
        background-image: linear-gradient(to right, #e5efff 1px, transparent 1px), linear-gradient(to bottom, #e5efff 1px, transparent 1px); 
        background-size: 4rem 4rem;
        mask-image: radial-gradient(ellipse 80% 50% at 50% 50%, black 40%, transparent 100%);
        -webkit-mask-image: radial-gradient(ellipse 80% 50% at 50% 50%, black 40%, transparent 100%);
      "></div>

      <!-- 主要内容 -->
      <div class="min-h-[calc(100vh-4rem)] flex items-center justify-center px-4 py-16">
        <div class="text-center space-y-6 relative">
          <!-- Gradient Accent -->
          <div class="absolute top-0 left-1/2 -translate-x-1/2 h-[600px] w-[600px] bg-blue-500/20 rounded-full blur-[120px] opacity-20 -z-[1]"></div>
          
          <!-- 404 大数字 -->
          <h1 class="text-9xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 text-transparent bg-clip-text select-none animate-slide-up">
            {{ error?.statusCode || '404' }}
          </h1>
          
          <!-- 标题和描述 -->
          <div class="space-y-2 animate-slide-up [animation-delay:200ms]">
            <h2 class="text-2xl font-semibold tracking-tight text-gray-900">{{ t('404.title') }}</h2>
            <p class="text-gray-600 max-w-[500px] mx-auto">{{ errorMessage }}</p>
          </div>

          <!-- 按钮 -->
          <div class="flex justify-center gap-4 animate-slide-up [animation-delay:400ms]">
            <button
              @click="goHome"
              class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-blue-600 rounded-xl hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl group"
            >
              <span class="flex items-center gap-2">
                <span>{{ t('404.backHome') }}</span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out forwards;
  opacity: 0;
}
</style>