export type FormRequest = {
  description: string
  formFields: Array<{
    id: string
    name: string
    type: string
  }>
  mode: 'bugReport' | 'general'
  apiKey: string
  provider?: string
  model?: string
  projectId?: string
}

export type AIProviderConfig = {
  apiKey: string
  model?: string
}

export type AIResponse = {
  content: string
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export type ProjectInfo = {
  id: string
  environment: string
  info: string
  template: string
}

export type OpenAIResponse = {
  choices: Array<{
    message: {
      content: string
    }
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
  error?: {
    message: string
  }
}

export type MoonshotResponse = OpenAIResponse

export type ClaudeResponse = {
  content: string
  error?: {
    message: string
  }
} 