// ===== BLOG TYPES =====

export interface BlogPost {
  // Content metadata
  _path: string
  _id: string
  _draft?: boolean
  
  // Post metadata
  title: string
  description?: string
  img?: string
  alt?: string
  date: string
  tags?: string[]
  author?: string
  readingTime?: number
  
  // Content
  body?: any
  excerpt?: string
  
  // SEO
  ogImage?: string
  twitterImage?: string
  
  // Navigation
  _dir?: string
  _extension?: string
  _file?: string
}

export interface BlogTag {
  name: string
  count: number
  slug: string
}

export interface BlogAuthor {
  name: string
  bio?: string
  avatar?: string
  social?: {
    twitter?: string
    github?: string
    linkedin?: string
    website?: string
  }
}

export interface BlogCategory {
  name: string
  slug: string
  description?: string
  color?: string
}

export interface BlogPostSurround {
  prev?: BlogPost | null
  next?: BlogPost | null
}

export interface BlogListProps {
  posts: BlogPost[]
  loading?: boolean
  error?: any
}

export interface BlogPostProps {
  post: BlogPost
  mode?: 'page' | 'card' | 'list'
}

export interface BlogTagsProps {
  tags: string[]
  selectedTag?: string
}

export interface BlogNavigationProps {
  prev?: BlogPost | null
  next?: BlogPost | null
}

// ===== BLOG FILTERS =====
export interface BlogFilters {
  tag?: string
  category?: string
  author?: string
  search?: string
  limit?: number
  offset?: number
}

export interface BlogSearchResult {
  posts: BlogPost[]
  total: number
  hasMore: boolean
}

// ===== BLOG CONFIGURATION =====
export interface BlogConfig {
  postsPerPage: number
  excerptLength: number
  enableComments: boolean
  enableSearch: boolean
  enableTags: boolean
  enableCategories: boolean
  enableAuthors: <AUTHORS>
  enableReadingTime: boolean
  enableSocialSharing: boolean
  dateFormat: string
  defaultImage: string
}

// ===== BLOG EVENTS =====
export interface BlogEvents {
  'tag-selected': (tag: string) => void
  'post-clicked': (post: BlogPost) => void
  'search-submitted': (query: string) => void
  'load-more': () => void
}

// ===== CONTENT QUERY TYPES =====
export interface ContentQueryBuilder {
  where(query: any): ContentQueryBuilder
  sort(options: any): ContentQueryBuilder
  limit(count: number): ContentQueryBuilder
  skip(count: number): ContentQueryBuilder
  find(): Promise<BlogPost[]>
  findOne(): Promise<BlogPost>
  findSurround(path: string): Promise<[BlogPost | null, BlogPost | null]>
}

// ===== UTILITY TYPES =====
export type BlogPostMode = 'page' | 'card' | 'list'
export type BlogSortOrder = 'asc' | 'desc'
export type BlogSortField = 'date' | 'title' | 'readingTime'

export interface BlogSort {
  field: BlogSortField
  order: BlogSortOrder
}

// ===== READING TIME CALCULATION =====
export interface ReadingTimeResult {
  text: string
  minutes: number
  time: number
  words: number
}

// ===== IMAGE HANDLING =====
export interface BlogImage {
  src: string
  alt: string
  width?: number
  height?: number
  loading?: 'lazy' | 'eager'
  placeholder?: string
}

// ===== SOCIAL SHARING =====
export interface SocialShareOptions {
  url: string
  title: string
  description?: string
  image?: string
  hashtags?: string[]
}

export interface SocialPlatform {
  name: string
  icon: string
  shareUrl: (options: SocialShareOptions) => string
  color: string
}

// ===== BLOG ANALYTICS =====
export interface BlogAnalytics {
  views: number
  likes: number
  shares: number
  comments: number
  readingProgress: number
}

// ===== BLOG COMMENTS =====
export interface BlogComment {
  id: string
  author: string
  email?: string
  website?: string
  content: string
  date: string
  replies?: BlogComment[]
  approved: boolean
}

export interface BlogCommentForm {
  author: string
  email: string
  website?: string
  content: string
  parentId?: string
}
