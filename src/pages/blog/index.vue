<template>
  <div class="blog-layout">
    <!-- Hero Section -->
    <section class="blog-hero">
      <div class="container">
        <h1>{{ $t('blog.title') }}</h1>
        <p>{{ $t('blog.description') }}</p>
      </div>
    </section>

    <!-- Tags Filter -->
    <OrganismBlogTags 
      :tags="availableTags" 
      :selected-tag="selectedTag"
      @tag-selected="handleTagSelected"
    />

    <!-- Blog Posts List -->
    <main class="blog-main">
      <TemplateBlogList 
        :posts="filteredPosts"
        :loading="pending"
        :error="error"
      />
    </main>
  </div>
</template>

<script setup lang="ts">
import type { BlogPost } from '~/types/blog'

// Meta and SEO
definePageMeta({
  layout: 'default'
})

useSeoMeta({
  title: 'Blog - Fillify',
  description: 'Discover the latest insights, tutorials, and updates about AI-powered form filling and automation.',
  ogTitle: 'Blog - Fillify',
  ogDescription: 'Discover the latest insights, tutorials, and updates about AI-powered form filling and automation.',
  ogImage: '/og/blog-og.png',
  twitterCard: 'summary_large_image'
})

// Reactive state
const selectedTag = ref<string>('')
const route = useRoute()

// Initialize selected tag from query params
onMounted(() => {
  if (route.query.tag && typeof route.query.tag === 'string') {
    selectedTag.value = route.query.tag
  }
})

// Fetch blog posts
const { data: posts, pending, error } = await useLazyAsyncData('blog-posts', () => 
  queryContent<BlogPost>('/blog')
    .where({ _draft: { $ne: true } })
    .sort({ date: -1 })
    .find()
)

// Compute available tags
const availableTags = computed(() => {
  if (!posts.value) return []
  
  const tagSet = new Set<string>()
  posts.value.forEach(post => {
    if (post.tags && Array.isArray(post.tags)) {
      post.tags.forEach(tag => tagSet.add(tag))
    }
  })
  
  return Array.from(tagSet).sort()
})

// Filter posts by selected tag
const filteredPosts = computed(() => {
  if (!posts.value) return []
  if (!selectedTag.value) return posts.value
  
  return posts.value.filter(post => 
    post.tags && post.tags.includes(selectedTag.value)
  )
})

// Handle tag selection
const handleTagSelected = (tag: string) => {
  selectedTag.value = tag
  
  // Update URL query params
  const router = useRouter()
  router.push({
    query: tag ? { tag } : {}
  })
}

// Watch for route changes to update selected tag
watch(() => route.query.tag, (newTag) => {
  selectedTag.value = typeof newTag === 'string' ? newTag : ''
})
</script>

<style scoped>
.blog-layout {
  min-height: 100vh;
}
</style>
