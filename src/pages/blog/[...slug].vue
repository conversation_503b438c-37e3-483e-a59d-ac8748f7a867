<template>
  <div class="blog-layout">
    <main class="blog-main">
      <div v-if="pending" class="loading">
        <div class="spinner"></div>
      </div>
      
      <div v-else-if="error" class="error-state">
        <div class="container">
          <h1>{{ $t('blog.post.notFound') }}</h1>
          <p>{{ $t('blog.post.notFoundDescription') }}</p>
          <NuxtLink to="/blog" class="back-link">
            {{ $t('blog.post.backToBlog') }}
          </NuxtLink>
        </div>
      </div>
      
      <article v-else-if="post" class="blog-post">
        <TemplateBlogPost 
          :post="post"
          mode="page"
        />
        
        <!-- Post Navigation -->
        <MoleculePostSurround 
          v-if="surround"
          :prev="surround[0]"
          :next="surround[1]"
        />
      </article>
    </main>
  </div>
</template>

<script setup lang="ts">
import type { BlogPost } from '~/types/blog'

const route = useRoute()
const slug = route.params.slug as string[]

// Fetch the blog post
const { data: post, pending, error } = await useLazyAsyncData(`blog-post-${slug.join('/')}`, () =>
  queryContent<BlogPost>('/blog', ...slug)
    .where({ _draft: { $ne: true } })
    .findOne()
)

// Fetch surrounding posts for navigation
const { data: surround } = await useLazyAsyncData(`blog-surround-${slug.join('/')}`, () =>
  queryContent<BlogPost>('/blog')
    .where({ _draft: { $ne: true } })
    .sort({ date: -1 })
    .findSurround(`/blog/${slug.join('/')}`)
)

// Handle 404 error
if (error.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Blog post not found'
  })
}

// SEO and meta tags
watchEffect(() => {
  if (post.value) {
    const { title, description, img, date, tags } = post.value
    
    useSeoMeta({
      title: `${title} - Fillify Blog`,
      description: description || 'Read the latest insights about AI-powered form filling and automation.',
      ogTitle: `${title} - Fillify Blog`,
      ogDescription: description || 'Read the latest insights about AI-powered form filling and automation.',
      ogImage: img ? `/images/blog/${img}` : '/og/blog-post-og.png',
      ogType: 'article',
      articlePublishedTime: date,
      articleTag: tags,
      twitterCard: 'summary_large_image'
    })
  }
})

// JSON-LD structured data for SEO
useJsonld(() => {
  if (!post.value) return {}
  
  const { title, description, img, date, tags } = post.value
  
  return {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: title,
    description: description,
    image: img ? `/images/blog/${img}` : '/og/blog-post-og.png',
    datePublished: date,
    dateModified: date,
    author: {
      '@type': 'Organization',
      name: 'Fillify',
      url: 'https://fillify.tech'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Fillify',
      url: 'https://fillify.tech',
      logo: {
        '@type': 'ImageObject',
        url: 'https://fillify.tech/logo/Fillify-Logo.svg'
      }
    },
    keywords: tags?.join(', '),
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://fillify.tech/blog/${slug.join('/')}`
    }
  }
})

// Page meta
definePageMeta({
  layout: 'default'
})
</script>

<style scoped>
.blog-layout {
  min-height: 100vh;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-primary);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  text-align: center;
  
  .container {
    max-width: 600px;
    padding: var(--space-8);
  }
  
  h1 {
    margin-bottom: var(--space-4);
    color: var(--text-primary);
  }
  
  p {
    margin-bottom: var(--space-6);
    color: var(--text-secondary);
  }
  
  .back-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    background: var(--color-primary);
    color: var(--text-inverse);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-medium);
    transition: background var(--transition-fast);
    
    &:hover {
      background: var(--color-primary-dark);
    }
  }
}
</style>
