<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white flex items-center justify-center">
    <!-- Main Content -->
    <main class="w-full px-4 flex flex-col items-center">
      <div class="max-w-md w-full">
        <!-- 登录卡片 -->
        <div class="bg-white/80 backdrop-blur-sm py-8 px-4 shadow-lg rounded-lg sm:px-10">
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900">{{ t('signin.title') }}</h2>
            <p class="mt-2 text-sm text-gray-600">
              {{ t('signin.subtitle') }}
            </p>
          </div>
          
          <!-- Google Sign In Button -->
          <div class="mt-6 relative">
            <div id="googleSignInButton" class="flex justify-center w-full max-w-[320px] mx-auto" :class="{ 'opacity-50 pointer-events-none': isLoading }"></div>
            <!-- Loading Overlay -->
            <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center">
              <div class="animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent"></div>
            </div>
          </div>
          
          <!-- Features -->
          <div class="mt-8 border-t border-gray-200 pt-6">
            <h3 class="text-sm font-medium text-gray-500 mb-4">{{ t('signin.features.title') }}</h3>
            <ul class="space-y-4">
              <li class="flex items-center text-sm text-gray-600">
                <Sparkles class="h-5 w-5 text-blue-500 mr-3" />
                {{ t('signin.features.list.autoFill') }}
              </li>
              <li class="flex items-center text-sm text-gray-600">
                <Wand class="h-5 w-5 text-purple-500 mr-3" />
                {{ t('signin.features.list.api') }}
              </li>
              <li class="flex items-center text-sm text-gray-600">
                <Rocket class="h-5 w-5 text-orange-500 mr-3" />
                {{ t('signin.features.list.early') }}
              </li>
            </ul>
          </div>
        </div>
        
        <!-- Terms and Privacy -->
        <div class="mt-6 text-center text-xs text-gray-500">
          <span class="block">{{ t('signin.terms.prefix') }}</span>
          <span class="mt-1 inline-block">
            <NuxtLink :to="currentLocale === 'en' ? '/terms' : `/${currentLocale}/terms`" class="text-blue-600 hover:text-blue-800">
              {{ t('footer.links.terms') }}
            </NuxtLink>
            <span class="mx-1">{{ t('signin.terms.and') }}</span>
            <NuxtLink :to="currentLocale === 'en' ? '/privacy' : `/${currentLocale}/privacy`" class="text-blue-600 hover:text-blue-800">
              {{ t('footer.links.privacy') }}
            </NuxtLink>
          </span>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, inject, watch } from 'vue'
import { cookieUtil } from '~/utils/cookie'
import { Sparkles, Wand, Rocket } from 'lucide-vue-next'
import { useAuth } from '~/composables/useAuth'

// 类型定义
interface UserState {
  isLoggedIn: boolean
  credits: number
  userId: string | null
}

// 声明 google 全局变量
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void
          renderButton: (element: HTMLElement | null, options: any) => void
          disableAutoSelect: () => void
          revoke: (email: string, callback: () => void) => void
        }
      }
    }
  }
}

const { t, locale } = useI18n()
const router = useRouter()
const localePath = useLocalePath()
const { user, isLoggedIn, fetchUserData } = useAuth()
const isGoogleScriptLoaded = ref(false)
const isLoading = ref(false)
const config = useRuntimeConfig()
const googleClientId = config.public.GOOGLE_CLIENT_ID
const currentLocale = computed(() => locale.value)

// 获取类型安全的 userState
const userState = inject<UserState>('userState')

// 监听登录状态
watch(isLoggedIn, (newValue) => {
  if (newValue) {
    navigateTo(localePath('/dashboard'))
  }
}, { immediate: true })

// 处理Google登录回调
const handleCredentialResponse = async (response: any) => {
  isLoading.value = true
  try {
    const res = await fetch(`${config.public.apiBase}/api/auth/google-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ credential: response.credential }),
    })
    const data = await res.json()
    
    if (data.success) {
      user.value = data.user
      cookieUtil.setCookie('xToken', data.user.id, {
        expires: 7,
        path: '/'
      })
      
      // 更新全局状态
      if (userState) {
        userState.isLoggedIn = true
        userState.credits = data.user.credits || 3
        userState.userId = data.user.id
        localStorage.setItem('userState', JSON.stringify(userState))
      }
      
      // 等待状态更新完成
      await nextTick()
      // 强制刷新用户数据
      await fetchUserData()
      
      // 使用 try-catch 确保导航成功
      try {
        const currentLang = locale.value
        const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
        await navigateTo(dashboardPath, { replace: true })
      } catch (navError) {
        console.error('Navigation error:', navError)
        // 如果导航失败，使用 window.location
        window.location.href = localePath('/dashboard')
      }
    }
  } catch (error) {
    // 只在开发环境输出错误
    if (process.dev) {
      console.error('Login error:', error)
    }
  } finally {
    isLoading.value = false
  }
}

// 确保在使用前检查 googleClientId
if (!googleClientId && process.dev) {
  console.error('Google Client ID is not configured')
}

// 初始化 Google 登录按钮
const initGoogleButton = () => {
  if (!window.google || !isGoogleScriptLoaded.value) return

  const button = document.getElementById('googleSignInButton')
  if (!button) return

  window.google.accounts.id.initialize({
    client_id: googleClientId,
    callback: handleCredentialResponse,
    ux_mode: 'popup'
  })

  window.google.accounts.id.renderButton(button, {
    type: 'standard',
    theme: 'outline',
    size: 'large',
    width: 320  // 设置固定宽度
  })
}

// 加载 Google 登录脚本并初始化按钮
onMounted(() => {
  const script = document.createElement('script')
  script.src = 'https://accounts.google.com/gsi/client'
  script.onload = () => {
    isGoogleScriptLoaded.value = true
    initGoogleButton()
  }
  document.head.appendChild(script)
})

// SEO
useHead({
  title: t('signin.seo.title'),
  meta: [
    { name: 'description', content: t('signin.seo.description') },
  ],
})
</script>